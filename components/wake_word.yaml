micro_wake_word:
  id: mww
  microphone: i2s_mic
  vad:
  models:
    - model: okay_nabu
    - model: hey_jarvis
    - model: alexa
  on_wake_word_detected:
    - media_player.speaker.play_on_device_media_file:
        media_file: wake_word_triggered
        announcement: true
    - delay: 300ms
    - voice_assistant.start:
        wake_word: !lambda return wake_word;

script:
  - id: start_wake_word
    then:
      - if:
          condition:
            - not:
                - voice_assistant.is_running:
          then:
            - micro_wake_word.start:

  - id: stop_wake_word
    then:
      - micro_wake_word.stop:
